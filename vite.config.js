import { fileURLToPath, URL } from 'node:url'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  server: {
    port: 3000,
    open: true
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks: {
          // 将 node_modules 中的依赖打包到 vendor 文件中
          vendor: ['vue', 'vue-router','element-plus','echarts','@univerjs/core','@univerjs/sheets','@univerjs/ui','@visactor/vchart','@visactor/vtable','@visactor/vue-vtable','@kjgl77/datav-vue3','@kangc/v-md-editor'], // 添加你的主要依赖
          // 如果使用了其他较大的库，可以单独拆分成一个 chunk
          // 例如: elementPlus: ['element-plus'],
        },
        chunkFileNames: 'js/[name]-[hash].js',
        entryFileNames: 'js/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash][extname]'
      }
    },
    chunkSizeWarningLimit: 1000, // 设置 chunk 大小警告限制
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    }
  },
  // 预加载优化
  optimizeDeps: {
    include: [
      'vue',
      'vue-router',
      'element-plus',
      'echarts',
      '@univerjs/core',
      '@univerjs/sheets',
      '@univerjs/ui',
      '@visactor/vchart',
      '@visactor/vtable',
      '@visactor/vue-vtable',
      '@kjgl77/datav-vue3',
      '@kangc/v-md-editor'
    ],
    exclude: []
  }
})
